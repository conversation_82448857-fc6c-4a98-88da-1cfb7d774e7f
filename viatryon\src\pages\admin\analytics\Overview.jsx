import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { toast } from 'react-toastify';
import { Eye, Users, TrendingUp, Clock } from 'lucide-react';

const Overview = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overviewData, setOverviewData] = useState(null);

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        
        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch both regular and enhanced overview data
        const [overviewResponse, enhancedResponse] = await Promise.all([
          fetch(`${apiUrl}/api/analytics/admin/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }),
          fetch(`${apiUrl}/api/analytics/admin/enhanced-overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          })
        ]);

        if (!overviewResponse.ok || !enhancedResponse.ok) {
          throw new Error('Failed to fetch overview data');
        }

        const [overviewData, enhancedData] = await Promise.all([
          overviewResponse.json(),
          enhancedResponse.json()
        ]);

        // Combine the data
        const combinedData = {
          ...overviewData,
          ...enhancedData,
          // Calculate additional metrics
          avgSessionDuration: overviewData.totalSessions > 0 ?
            (overviewData.totalDuration || 0) / overviewData.totalSessions : 0,
          sessionsGrowth: overviewData.tryOnsGrowth || 0,
          clientsGrowth: 0, // Will be calculated from previous period
          durationGrowth: 0, // Will be calculated from previous period
          successRateGrowth: 0, // Will be calculated from previous period

          // Enhanced metrics
          engagementScore: enhancedData.avgEngagementScore || 0,
          qualityScore: (
            (enhancedData.avgHandDetectionAccuracy || 0) +
            (enhancedData.avgBackgroundRemovalQuality || 0)
          ) / 2 * 100,

          // Session trends (mock data for now, should come from backend)
          sessionTrends: overviewData.trends?.map(trend => ({
            date: trend.date,
            sessions: trend.tryOns,
            successfulSessions: trend.conversions
          })) || [],

          // Feature success rates
          featureSuccessRates: [
            {
              feature: 'Hand Detection',
              successRate: (enhancedData.avgHandDetectionAccuracy || 0) * 100
            },
            {
              feature: 'Background Removal',
              successRate: (enhancedData.avgBackgroundRemovalQuality || 0) * 100
            },
            {
              feature: 'Screenshot Capture',
              successRate: enhancedData.totalSessions > 0 ?
                (enhancedData.totalScreenshots / enhancedData.totalSessions) * 100 : 0
            },
            {
              feature: 'Share Feature',
              successRate: enhancedData.shareRate || 0
            }
          ],

          // Recent activity (mock data for now)
          recentActivity: [
            {
              type: 'high_volume',
              title: 'High Try-On Volume',
              description: `${enhancedData.totalSessions || 0} sessions in selected period`,
              timestamp: '2 hours ago'
            },
            {
              type: 'success_rate',
              title: 'Quality Metrics',
              description: `${Math.round((enhancedData.avgHandDetectionAccuracy || 0) * 100)}% hand detection accuracy`,
              timestamp: '4 hours ago'
            },
            {
              type: 'new_client',
              title: 'Engagement Score',
              description: `Average engagement: ${Math.round(enhancedData.avgEngagementScore || 0)}%`,
              timestamp: '6 hours ago'
            }
          ]
        };

        setOverviewData(combinedData);
      } catch (err) {
        console.error('Error fetching overview data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading overview</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {overviewData?.totalSessions?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Eye className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.sessionsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.sessionsGrowth >= 0 ? '+' : ''}{overviewData?.sessionsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Clients</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {overviewData?.activeClients?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.clientsGrowth >= 0 ? '+' : ''}{overviewData?.clientsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Session Duration</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(overviewData?.avgSessionDuration || 0)}s
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <Clock className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.durationGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.durationGrowth >= 0 ? '+' : ''}{overviewData?.durationGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Engagement Score</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(overviewData?.engagementScore || 0)}%
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-purple-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${overviewData?.successRateGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {overviewData?.successRateGrowth >= 0 ? '+' : ''}{overviewData?.successRateGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quality Score</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(overviewData?.qualityScore || 0)}%
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-orange-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-600">
              Hand detection & background removal
            </span>
          </div>
        </motion.div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={overviewData?.sessionTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Sessions"
                />
                <Line
                  type="monotone"
                  dataKey="successfulSessions"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981' }}
                  name="Successful Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Feature Success Rates */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Feature Success Rates</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={overviewData?.featureSuccessRates}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="feature" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="successRate" fill="#2D8C88" name="Success Rate" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-xl shadow-sm overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {overviewData?.recentActivity?.map((activity, index) => (
            <div key={index} className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  {activity.type === 'new_client' && <Users className="h-6 w-6 text-[#2D8C88]" />}
                  {activity.type === 'high_volume' && <Eye className="h-6 w-6 text-blue-500" />}
                  {activity.type === 'success_rate' && <TrendingUp className="h-6 w-6 text-green-500" />}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <p className="text-sm text-gray-500">{activity.description}</p>
                </div>
                <div className="ml-auto">
                  <p className="text-sm text-gray-500">{activity.timestamp}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default Overview;
